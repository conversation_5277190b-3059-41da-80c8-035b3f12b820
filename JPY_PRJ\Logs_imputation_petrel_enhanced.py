# # Missing Logs Imputation – **Pet<PERSON>‑connected** workflow with Interactive Selection
#
# This script connects directly to the **currently open Petrel project** with Cegal Prizm Python Tool Pro,
# provides console-based interactive menus for well and log selection, pulls the selected global well logs
# for every well, applies machine‑learning models to fill in missing values, and (optionally) writes the
# imputed logs back into Petrel.
#
# *Enhanced with console-based interactive well and log selection - Generated 2025-06-20*.
#
# USAGE:
# 1. Run this script in Petrel's Python environment (not Jupyter)
# 2. Follow the console prompts to select wells and logs
# 3. The script will automatically process the data and generate imputed logs
# 4. Optionally write results back to Petrel
#
# REQUIREMENTS:
# - Active Petrel project with well log data
# - Cegal Prizm Python Tool Pro
# - Required Python packages: numpy, pandas, xgboost, lightgbm, catboost, sklearn, plotly

# Core libraries
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# ML libraries
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error

# Plotting (optional)
import plotly.express as px

# Petrel connection
from cegalprizm.pythontool import PetrelConnection

petrel = PetrelConnection()
print(f'Connected to Petrel project: {petrel.get_current_project_name()}')

# Configuration: Set to True for automatic selection (useful for testing/non-interactive environments)
AUTO_MODE = False  # Set to True to use defaults automatically without user input

# Prediction mode options
PREDICTION_MODES = {
    1: "Fill Missing Values Only",
    2: "Cross-Validation Test",
    3: "Re-predict Entire Log"
}


# ## Console-based Interactive Selection Functions

def console_select_multiple(options, prompt, default_selections=None, max_selections=None, auto_mode=False):
    """
    Console-based multiple selection function.

    Args:
        options: List of available options
        prompt: Description of what to select
        default_selections: List of default selections (by name)
        max_selections: Maximum number of selections allowed
        auto_mode: If True, automatically use defaults without user input

    Returns:
        List of selected option names
    """
    if not options:
        print("No options available for selection.")
        return []

    print(f"\n{prompt}")
    print("Available options:")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")

    # Set up defaults
    default_indices = []
    if default_selections:
        for default in default_selections:
            if default in options:
                default_indices.append(options.index(default) + 1)

    if default_indices:
        print(f"\nDefault selections: {', '.join([str(i) for i in default_indices])}")

    # Auto mode: use defaults without user input
    if auto_mode:
        if default_indices:
            selected_indices = default_indices
            selected_options = [options[i-1] for i in selected_indices]
            print(f"Auto-selected: {', '.join(selected_options)}")
            return selected_options
        else:
            # Fallback to first few options
            max_select = min(max_selections or 5, len(options))
            selected_options = options[:max_select]
            print(f"Auto-selected (fallback): {', '.join(selected_options)}")
            return selected_options

    print(f"\nEnter your selections:")
    print("- Enter numbers separated by commas (e.g., 1,3,5)")
    print("- Enter 'all' to select all options")
    print("- Press Enter to use defaults (if available)")
    if max_selections:
        print(f"- Maximum {max_selections} selections allowed")

    while True:
        try:
            user_input = input("Selection: ").strip()

            # Use defaults if empty input and defaults exist
            if not user_input and default_indices:
                selected_indices = default_indices
                break

            # Select all
            if user_input.lower() == 'all':
                if max_selections and len(options) > max_selections:
                    print(f"Cannot select all - maximum {max_selections} selections allowed.")
                    continue
                selected_indices = list(range(1, len(options) + 1))
                break

            # Parse comma-separated numbers
            if user_input:
                selected_indices = [int(x.strip()) for x in user_input.split(',')]

                # Validate selections
                if any(i < 1 or i > len(options) for i in selected_indices):
                    print(f"Invalid selection. Please enter numbers between 1 and {len(options)}")
                    continue

                if max_selections and len(selected_indices) > max_selections:
                    print(f"Too many selections. Maximum {max_selections} allowed.")
                    continue

                break
            else:
                print("Please enter a selection or press Enter for defaults.")

        except (ValueError, EOFError, KeyboardInterrupt):
            if default_indices:
                print("\nUsing default selections...")
                selected_indices = default_indices
                break
            else:
                print("Invalid input. Please enter numbers separated by commas.")

    # Convert indices to option names
    selected_options = [options[i-1] for i in selected_indices]
    print(f"Selected: {', '.join(selected_options)}")
    return selected_options


def console_select_single(options, prompt, default_selection=None, auto_mode=False):
    """
    Console-based single selection function.

    Args:
        options: List of available options
        prompt: Description of what to select
        default_selection: Default selection (by name)
        auto_mode: If True, automatically use default without user input

    Returns:
        Selected option name
    """
    if not options:
        print("No options available for selection.")
        return None

    print(f"\n{prompt}")
    print("Available options:")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")

    # Set up default
    default_index = None
    if default_selection and default_selection in options:
        default_index = options.index(default_selection) + 1
        print(f"\nDefault selection: {default_index}")

    # Auto mode: use default without user input
    if auto_mode:
        if default_index:
            selected_option = options[default_index-1]
            print(f"Auto-selected: {selected_option}")
            return selected_option
        else:
            selected_option = options[0]
            print(f"Auto-selected (fallback): {selected_option}")
            return selected_option

    print(f"\nEnter your selection:")
    print("- Enter a number (1-{})".format(len(options)))
    print("- Press Enter to use default (if available)")

    while True:
        try:
            user_input = input("Selection: ").strip()

            # Use default if empty input and default exists
            if not user_input and default_index:
                selected_index = default_index
                break

            # Parse number
            if user_input:
                selected_index = int(user_input)

                # Validate selection
                if selected_index < 1 or selected_index > len(options):
                    print(f"Invalid selection. Please enter a number between 1 and {len(options)}")
                    continue

                break
            else:
                print("Please enter a selection or press Enter for default.")

        except (ValueError, EOFError, KeyboardInterrupt):
            if default_index:
                print("\nUsing default selection...")
                selected_index = default_index
                break
            else:
                print("Invalid input. Please enter a number.")

    # Convert index to option name
    selected_option = options[selected_index-1]
    print(f"Selected: {selected_option}")
    return selected_option


# ## 1 – Interactive Well and Log Selection

# Create console-based interactive selection interface
print('Setting up interactive selection interface...')

# Get all available global well logs
available_logs = {}
try:
    for log in petrel.global_well_logs:
        if hasattr(log, 'petrel_name'):
            available_logs[log.petrel_name] = log

    log_names = sorted(available_logs.keys())
    print(f'Found {len(log_names)} global well logs')

    if len(log_names) > 0:
        print(f'First few logs: {log_names[:min(5, len(log_names))]}')

except Exception as e:
    print(f'ERROR accessing global well logs: {str(e)}')
    available_logs = {}
    log_names = []

# Get all wells (using the standard approach from working examples)
print('Scanning available wells...')
try:
    # Use the standard approach that works in other scripts
    wells = list(petrel.wells)
    well_names = [w.petrel_name for w in wells]
    print(f'Found {len(wells)} wells')

    if len(wells) > 0:
        print(f'First few wells: {well_names[:min(3, len(well_names))]}')

except Exception as e:
    print(f'ERROR accessing wells: {str(e)}')
    print(f'Type of petrel.wells: {type(petrel.wells)}')

    # Fallback: try manual iteration approach
    print('Trying fallback approach...')
    wells = []
    well_names = []
    try:
        for well in petrel.wells:
            if hasattr(well, 'petrel_name'):
                wells.append(well)
                well_names.append(well.petrel_name)
        print(f'Fallback found {len(wells)} wells')
    except Exception as e2:
        print(f'Fallback also failed: {str(e2)}')
        wells = []
        well_names = []

# Console-based well selection
if not wells or not well_names:
    print('ERROR: No wells found in the project!')
    print('Please ensure:')
    print('1. Petrel project is open and contains wells')
    print('2. Wells are properly loaded in the project')
    print('3. Python Tool Pro connection is working correctly')
    raise ValueError("No wells available for selection")

default_wells = well_names[:min(5, len(well_names))]  # First 5 wells as default
selected_well_names = console_select_multiple(
    options=well_names,
    prompt="Select Wells for Analysis:",
    default_selections=default_wells,
    max_selections=None,
    auto_mode=AUTO_MODE
)

# Console-based input logs selection
if not log_names:
    print('ERROR: No global well logs found in the project!')
    print('Please ensure:')
    print('1. The project contains well logs')
    print('2. Logs are properly loaded as global well logs')
    print('3. Check the Input/Global Well Logs folder in Petrel')
    raise ValueError("No logs available for selection")

default_input_logs = ['GR', 'RHOB', 'NPHI', 'Vp'] if all(log in log_names for log in ['GR', 'RHOB', 'NPHI', 'Vp']) else log_names[:4] if len(log_names) >= 4 else log_names
selected_input_logs = console_select_multiple(
    options=log_names,
    prompt="Select Input Logs for ML Training:",
    default_selections=default_input_logs,
    max_selections=None,
    auto_mode=AUTO_MODE
)

# Console-based target log selection
default_target_log = 'Vs' if 'Vs' in log_names else log_names[0] if log_names else None
selected_target_log = console_select_single(
    options=log_names,
    prompt="Select Target Log for Imputation:",
    default_selection=default_target_log,
    auto_mode=AUTO_MODE
)

# Store selections for later use
log_selection_data = {
    'selected_wells': [w for w in wells if w.petrel_name in selected_well_names],
    'selected_well_names': selected_well_names,
    'selected_input_logs': selected_input_logs,
    'selected_target_log': selected_target_log,
    'available_logs': available_logs,
    'all_wells': wells
}

# Validate that we found the selected wells
if len(log_selection_data['selected_wells']) != len(selected_well_names):
    print(f"WARNING: Only found {len(log_selection_data['selected_wells'])} out of {len(selected_well_names)} selected wells")
    found_names = [w.petrel_name for w in log_selection_data['selected_wells']]
    missing_names = [name for name in selected_well_names if name not in found_names]
    print(f"Missing wells: {missing_names}")

print(f'\n✓ Selection completed:')
print(f'  - Wells: {len(selected_well_names)} selected')
print(f'  - Input logs: {len(selected_input_logs)} selected')
print(f'  - Target log: {selected_target_log}')

def display_selection_summary():
    """Display a comprehensive summary of current selections."""
    print("\n" + "="*60)
    print("SELECTION SUMMARY")
    print("="*60)
    print(f"Project: {petrel.get_current_project_name()}")
    print(f"Selected Wells ({len(selected_well_names)}):")
    for i, well_name in enumerate(selected_well_names, 1):
        print(f"  {i}. {well_name}")

    print(f"\nSelected Input Logs ({len(selected_input_logs)}):")
    for i, log_name in enumerate(selected_input_logs, 1):
        print(f"  {i}. {log_name}")

    print(f"\nTarget Log for Imputation:")
    print(f"  {selected_target_log}")

    total_logs = len(selected_input_logs) + 1  # input logs + target log
    print(f"\nTotal Logs to Process: {total_logs}")
    print("="*60)

# Display the summary
display_selection_summary()

# ## Prediction Mode Selection

print("\n" + "="*60)
print("PREDICTION MODE SELECTION")
print("="*60)

# Select prediction mode
mode_options = [f"{key}. {value}" for key, value in PREDICTION_MODES.items()]
print("\nAvailable prediction modes:")
for option in mode_options:
    print(f"  {option}")

selected_mode = console_select_single(
    options=list(PREDICTION_MODES.keys()),
    prompt="Select Prediction Mode:",
    default_selection=1,
    auto_mode=AUTO_MODE
)

prediction_mode = int(selected_mode) if selected_mode else 1
prediction_mode_name = PREDICTION_MODES[prediction_mode]

print(f"\n✓ Selected prediction mode: {prediction_mode_name}")

# Additional parameters for cross-validation mode
test_percentage = 25.0
if prediction_mode == 2:
    print(f"\nCross-validation parameters:")
    print(f"  - Test data percentage: {test_percentage}%")
    print(f"  - This will randomly hold out {test_percentage}% of complete data for validation")

print("="*60)


# ## 2 – Configure wells & apply selection

# Get selected data from console selections
selected_wells = log_selection_data['selected_wells']
selected_well_names = log_selection_data['selected_well_names']
selected_input_logs = log_selection_data['selected_input_logs']
selected_target_log = log_selection_data['selected_target_log']

print(f'\n✓ Configuration applied:')
print(f'Selected {len(selected_wells)} wells: {[w.petrel_name for w in selected_wells]}')
print(f'Selected input logs: {selected_input_logs}')
print(f'Selected target log: {selected_target_log}')

# Combine all log names
LOG_NAMES = selected_input_logs + [selected_target_log]
print(f'All logs to process: {LOG_NAMES}')

# Helper function to find global well logs by name
def find_global_well_logs_by_names(names):
    found_logs = []
    for name in names:
        if name in available_logs:
            found_logs.append(available_logs[name])
        else:
            print(f'Warning: Log {name} not found in available logs')
    return found_logs

logs = find_global_well_logs_by_names(LOG_NAMES)
print(f'Using {len(logs)} global logs: {[g.petrel_name for g in logs]}')

# Validate that we have both wells and logs selected
if not selected_wells:
    print('ERROR: No wells selected! Please run the console selection again.')
    raise ValueError("No wells selected")
if not logs:
    print('ERROR: No valid logs found! Please check your log selection.')
    raise ValueError("No valid logs found")
if selected_wells and logs:
    print('✓ Ready to load data from selected wells and logs')

# Additional validation: Check if selected logs exist in selected wells
print('\n--- Validating log availability in selected wells ---')
for well in selected_wells[:3]:  # Check first 3 wells as sample
    well_log_names = [log.petrel_name for log in well.logs]
    print(f"\nWell: {well.petrel_name}")
    for log_name in LOG_NAMES:
        status = "✓" if log_name in well_log_names else "✗"
        print(f"  {status} {log_name}")

print(f'\n✓ Validation completed. Proceeding with {len(selected_wells)} wells and {len(logs)} logs.')


# ## 3 – Load log data from Petrel (Fixed with Error Handling)

# Ensure we have the required variables from previous sections
try:
    # Variables should be available from console selection
    if 'selected_wells' not in locals():
        print('ERROR: Console selection not completed. Please run section 1 first.')
        raise ValueError("Missing selection data")

    if 'logs' not in locals():
        # Use the console selections
        available_logs = log_selection_data['available_logs']

        def find_global_well_logs_by_names(names):
            found_logs = []
            for name in names:
                if name in available_logs:
                    found_logs.append(available_logs[name])
            return found_logs

        logs = find_global_well_logs_by_names(LOG_NAMES)
        
    print(f'Loading data from {len(selected_wells)} wells with {len(logs)} logs...')
    
    # Initialize empty DataFrame
    well_data = pd.DataFrame()
    
    # Load data from each selected well
    for i, w in enumerate(selected_wells):
        print(f'Processing well {i+1}/{len(selected_wells)}: {w.petrel_name}')
        try:
            df = w.logs_dataframe(logs)      # returns MD‑indexed DataFrame
            if not df.empty:
                df['WELL'] = w.petrel_name
                well_data = pd.concat([well_data, df], ignore_index=False)
                print(f'  ✓ Loaded {len(df)} samples')
            else:
                print(f'  ⚠ No data found for this well')
        except Exception as e:
            print(f'  ✗ Error loading data: {str(e)}')
    
    # Reset index to make MD a column
    if not well_data.empty:
        well_data.reset_index(drop=False, inplace=True)  # MD becomes column
        print(f'\n✓ Combined DataFrame shape: {well_data.shape}')
        print(f'Columns: {list(well_data.columns)}')
        print('\nFirst 5 rows of loaded data:')
        print(well_data.head().to_string())
    else:
        print('\n✗ No data loaded! Check your well and log selections.')
        
except Exception as e:
    print(f'Error in data loading: {str(e)}')
    print('Make sure you have run the previous cells to select wells and logs.')


# ## 4 – Basic cleaning (remove obvious spikes)

# Clean GR if it's in the selected logs
if 'GR' in well_data.columns:
    GR_MAX = 300
    well_data['GR'] = np.where(well_data['GR'] <= GR_MAX, well_data['GR'], np.nan)
    print(f'Cleaned GR values > {GR_MAX}')
    
# Clean NPHI if it's in the selected logs
if 'NPHI' in well_data.columns:
    well_data['NPHI'] = np.where(
        (well_data['NPHI'] >= 0) & (well_data['NPHI'] <= 1),
        well_data['NPHI'], np.nan
    )
    print('Cleaned NPHI values outside [0, 1] range')
    
# Clean RHOB if it's in the selected logs
if 'RHOB' in well_data.columns:
    well_data['RHOB'] = np.where(
        (well_data['RHOB'] >= 1.0) & (well_data['RHOB'] <= 3.5),
        well_data['RHOB'], np.nan
    )
    print('Cleaned RHOB values outside [1.0, 3.5] range')

print('\nData summary after cleaning:')
print(well_data.describe().T.to_string())


# ## 5 – Coverage per log

# Calculate coverage for selected logs only
log_columns = [col for col in LOG_NAMES if col in well_data.columns]
coverage = 1.0 - well_data[log_columns].isna().mean()
print('Data coverage per log:')
for log_name, cov in coverage.items():
    print(f'  {log_name}: {cov:.2%}')

# Visualize coverage
fig = px.bar(coverage, labels={'value':'Coverage'}, title='Relative data coverage for selected logs')
fig.show()


# ## 6 – Imputation utility with enhanced ML models

def impute_logs(df, depth_col, feature_cols, targets, prediction_mode=1, test_percentage=25.0):
    """
    Return DataFrame with *_pred, *_imputed, *_error columns and model results.

    Args:
        df: Input DataFrame
        depth_col: Depth column name
        feature_cols: Feature column names
        targets: Target column names
        prediction_mode: 1=Fill missing only, 2=Cross-validation, 3=Re-predict all
        test_percentage: Percentage for cross-validation test

    Returns:
        tuple: (results_df, model_results_dict)
    """
    res = df.copy()
    model_results = {}

    # Configure ML models with user's preferred hyperparameters
    boosters = [
        ('EXTREME BOOST REGRESSOR', XGBRegressor(
            n_estimators=300,
            tree_method='gpu_hist',
            learning_rate=0.05,
            early_stopping_rounds=100,
            random_state=42
        )),
        ('LGBM REGRESSOR', LGBMRegressor(
            device='gpu',
            gpu_platform_id=1,
            gpu_device_id=0,
            n_estimators=300,
            random_state=42
        )),
        ('CATBOOST REGRESSOR', CatBoostRegressor(
            task_type='GPU',
            early_stopping_rounds=100,
            verbose=0,
            random_state=42
        ))
    ]
    feature_set = feature_cols + [depth_col]

    for tgt in targets:
        print(f'--- Processing {tgt} (Mode: {PREDICTION_MODES[prediction_mode]}) ---')

        # Prepare training data based on prediction mode
        if prediction_mode == 1:  # Fill missing values only
            train = res[res[tgt].notna()][feature_set + [tgt]].copy()
            print(f'Using {len(train)} complete samples for training')
        elif prediction_mode == 2:  # Cross-validation test
            complete_data = res[res[tgt].notna()][feature_set + [tgt]].copy()
            if len(complete_data) == 0:
                print('No complete data available for cross-validation')
                continue
            # Split complete data for validation
            train, test_data = train_test_split(complete_data, test_size=test_percentage/100, random_state=42)
            print(f'Cross-validation: {len(train)} training, {len(test_data)} test samples')
        else:  # prediction_mode == 3: Re-predict entire log
            train = res[res[tgt].notna()][feature_set + [tgt]].copy()
            print(f'Re-predicting entire log using {len(train)} training samples')

        if train.empty:
            print('No training data available, skipping.')
            continue

        X = train.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)
        y = train[tgt]

        # Split training data for model evaluation
        Xtr, Xval, ytr, yval = train_test_split(X, y, test_size=0.25, random_state=42)

        # Train and evaluate all models
        trained_models = {}
        model_performances = {}
        best_model, best_name, best_mae = None, None, float("inf")

        print(f'Training {len(boosters)} models on {len(Xtr)} samples...')
        for name, model in boosters:
            try:
                model.fit(Xtr, ytr)
                mae = mean_absolute_error(yval, model.predict(Xval))
                print(f'  {name}: MAE = {mae:.3f}')

                trained_models[name] = model
                model_performances[name] = mae

                if mae < best_mae:
                    best_model, best_name, best_mae = model, name, mae
            except Exception as e:
                print(f'  {name}: Failed - {str(e)}')

        if best_model is None:
            print('All models failed! Skipping this target.')
            continue

        print(f'✓ Best model: {best_name} (MAE={best_mae:.3f})')

        # Generate predictions for all models
        X_full = res[feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)
        all_model_predictions = {}

        for name, model in trained_models.items():
            try:
                preds = model.predict(X_full)
                all_model_predictions[name] = pd.Series(preds, index=res.index)
                # Add individual model predictions to results
                res[f'{tgt}_{name.replace(" ", "_").lower()}'] = preds
            except Exception as e:
                print(f'Error generating predictions for {name}: {str(e)}')

        # Best model predictions
        best_preds = best_model.predict(X_full)
        best_preds_series = pd.Series(best_preds, index=res.index)

        # Apply prediction mode logic
        if prediction_mode == 1:  # Fill missing values only
            res[f'{tgt}_pred'] = best_preds
            res[f'{tgt}_imputed'] = res[tgt].fillna(best_preds_series)
            missing_count = res[tgt].isna().sum()
            total_count = len(res)
            print(f'Imputed {missing_count}/{total_count} missing values ({missing_count/total_count:.1%})')

        elif prediction_mode == 2:  # Cross-validation test
            res[f'{tgt}_pred'] = best_preds
            res[f'{tgt}_imputed'] = res[tgt].fillna(best_preds_series)
            # Calculate validation metrics on held-out test data
            if 'test_data' in locals():
                X_test = test_data.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)
                y_test = test_data[tgt]
                test_preds = best_model.predict(X_test)
                test_mae = mean_absolute_error(y_test, test_preds)
                print(f'Cross-validation MAE on held-out data: {test_mae:.3f}')
                model_performances[f'{best_name}_CV'] = test_mae

        else:  # prediction_mode == 3: Re-predict entire log
            res[f'{tgt}_pred'] = best_preds
            res[f'{tgt}_imputed'] = best_preds_series  # Replace entire log with predictions
            print(f'Re-predicted entire log ({len(res)} samples)')

        # Calculate error metrics where original data exists
        res[f'{tgt}_error'] = np.abs(res[tgt] - best_preds_series) / res[tgt] * 100

        # Store model results for plotting
        model_results[tgt] = {
            'trained_models': trained_models,
            'model_performances': model_performances,
            'best_model_name': best_name,
            'best_mae': best_mae,
            'all_predictions': all_model_predictions,
            'best_predictions': best_preds_series,
            'original_data': res[tgt].copy(),
            'prediction_mode': prediction_mode
        }

    return res, model_results


# ## 7 – Run imputation with selected logs

DEPTH_COL = 'MD'
FEATURES = selected_input_logs  # Use selected input logs
TARGET = selected_target_log    # Use selected target log

print(f'\n{"="*60}')
print(f'RUNNING MACHINE LEARNING IMPUTATION')
print(f'{"="*60}')
print(f'Target log: {TARGET}')
print(f'Input features: {FEATURES}')
print(f'Depth column: {DEPTH_COL}')
print(f'Prediction mode: {prediction_mode_name}')

# Run the imputation with selected mode
results, model_results = impute_logs(
    df=well_data,
    depth_col=DEPTH_COL,
    feature_cols=FEATURES,
    targets=[TARGET],
    prediction_mode=prediction_mode,
    test_percentage=test_percentage
)

print(f'\n✓ Processing completed! Results shape: {results.shape}')
new_columns = [col for col in results.columns if any(suffix in col for suffix in ['_pred', '_imputed', '_error', '_extreme', '_lgbm', '_catboost'])]
print(f'New columns added: {len(new_columns)}')
for col in new_columns:
    print(f'  - {col}')

print('\nFirst 5 rows of key results:')
key_columns = ['MD', TARGET, f'{TARGET}_pred', f'{TARGET}_imputed', f'{TARGET}_error']
available_columns = [col for col in key_columns if col in results.columns]
print(results[available_columns].head().to_string())


# ## 8 – Model Comparison and Visualization

def create_model_comparison_plots(results_df, model_results, target_log):
    """Create comparison plots for all models and the target log"""

    print(f'\n{"="*60}')
    print('GENERATING MODEL COMPARISON PLOTS')
    print("="*60)

    if target_log not in model_results:
        print(f'No model results found for {target_log}')
        return

    target_data = model_results[target_log]

    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as mpatches

        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 10))

        # Get data
        md = results_df['MD']
        original = target_data['original_data']
        best_pred = target_data['best_predictions']
        all_preds = target_data['all_predictions']

        # Plot 1: Log comparison (depth vs values)
        ax1.plot(original, md, 'b-', linewidth=2, label=f'Original {target_log}', alpha=0.8)
        ax1.plot(best_pred, md, 'r-', linewidth=2, label=f'Best Model ({target_data["best_model_name"]})')

        # Plot all model predictions with different colors
        colors = ['green', 'orange', 'purple', 'brown', 'pink']
        for i, (model_name, predictions) in enumerate(all_preds.items()):
            if model_name != target_data["best_model_name"]:
                color = colors[i % len(colors)]
                ax1.plot(predictions, md, '--', color=color, linewidth=1,
                        label=model_name, alpha=0.7)

        ax1.set_ylabel('Measured Depth (MD)')
        ax1.set_xlabel(f'{target_log} Values')
        ax1.set_title(f'{target_log} - Original vs Predicted\n(Mode: {PREDICTION_MODES[target_data["prediction_mode"]]})')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        ax1.invert_yaxis()  # Depth increases downward

        # Plot 2: Cross-plot (original vs predicted)
        # Only plot where original data exists
        valid_mask = ~original.isna()
        if valid_mask.any():
            original_valid = original[valid_mask]
            best_pred_valid = best_pred[valid_mask]

            ax2.scatter(original_valid, best_pred_valid, alpha=0.6, s=20,
                       label=f'Best Model ({target_data["best_model_name"]})')

            # Plot other models
            for i, (model_name, predictions) in enumerate(all_preds.items()):
                if model_name != target_data["best_model_name"]:
                    pred_valid = predictions[valid_mask]
                    color = colors[i % len(colors)]
                    ax2.scatter(original_valid, pred_valid, alpha=0.4, s=15,
                               color=color, label=model_name)

            # Perfect prediction line
            min_val = min(original_valid.min(), best_pred_valid.min())
            max_val = max(original_valid.max(), best_pred_valid.max())
            ax2.plot([min_val, max_val], [min_val, max_val], 'k--',
                    linewidth=1, label='Perfect Prediction')

            ax2.set_xlabel(f'Original {target_log}')
            ax2.set_ylabel(f'Predicted {target_log}')
            ax2.set_title(f'Cross-plot: Original vs Predicted\n(MAE: {target_data["best_mae"]:.3f})')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # Calculate and display R²
            from sklearn.metrics import r2_score
            r2 = r2_score(original_valid, best_pred_valid)
            ax2.text(0.05, 0.95, f'R² = {r2:.3f}', transform=ax2.transAxes,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        else:
            ax2.text(0.5, 0.5, 'No original data available\nfor cross-plot comparison',
                    ha='center', va='center', transform=ax2.transAxes, fontsize=12)
            ax2.set_title('Cross-plot: Original vs Predicted')

        plt.tight_layout()

        # Display model performance summary
        print('\nModel Performance Summary:')
        print('-' * 40)
        for model_name, mae in target_data['model_performances'].items():
            marker = '★' if model_name == target_data['best_model_name'] else ' '
            print(f'{marker} {model_name}: MAE = {mae:.3f}')

        # Show the plot
        plt.show()

        print('✓ Comparison plots generated successfully!')

    except ImportError:
        print('⚠ Matplotlib not available. Skipping plot generation.')
        print('Model Performance Summary:')
        print('-' * 40)
        for model_name, mae in target_data['model_performances'].items():
            marker = '★' if model_name == target_data['best_model_name'] else ' '
            print(f'{marker} {model_name}: MAE = {mae:.3f}')

    except Exception as e:
        print(f'Error generating plots: {str(e)}')
        print('Model Performance Summary:')
        print('-' * 40)
        for model_name, mae in target_data['model_performances'].items():
            marker = '★' if model_name == target_data['best_model_name'] else ' '
            print(f'{marker} {model_name}: MAE = {mae:.3f}')

# Generate comparison plots
create_model_comparison_plots(results, model_results, TARGET)


# ## 9 – Interactive Write-back and Connection Management

def interactive_write_back_prompt():
    """Interactive prompt for write-back decision"""

    print(f'\n{"="*60}')
    print('WRITE-BACK OPTIONS')
    print("="*60)

    options = [
        "1. Write results back to Petrel",
        "2. End session without write-back",
        "3. Continue working (keep results in memory)"
    ]

    print("\nWhat would you like to do with the results?")
    for option in options:
        print(f"  {option}")

    choice = console_select_single(
        options=[1, 2, 3],
        prompt="Select your choice:",
        default_selection=3,
        auto_mode=AUTO_MODE
    )

    return int(choice) if choice else 3

def interactive_connection_management():
    """Interactive prompt for connection management"""

    print(f'\n{"="*60}')
    print('SESSION MANAGEMENT')
    print("="*60)

    options = [
        "1. End Python session",
        "2. Keep session active for further analysis"
    ]

    print("\nHow would you like to end the session?")
    for option in options:
        print(f"  {option}")

    choice = console_select_single(
        options=[1, 2],
        prompt="Select your choice:",
        default_selection=2,
        auto_mode=AUTO_MODE
    )

    return int(choice) if choice else 2

# Interactive write-back decision
write_back_choice = interactive_write_back_prompt()

if write_back_choice == 1:
    print(f'\n{"="*60}')
    print('WRITING RESULTS BACK TO PETREL')
    print("="*60)

    # Determine which log to write back based on prediction mode
    if prediction_mode == 3:  # Re-predict entire log
        log_to_write = f'{TARGET}_imputed'
        log_suffix = '_repredicted'
    else:  # Fill missing or cross-validation
        log_to_write = f'{TARGET}_imputed'
        log_suffix = '_imputed'

    new_log_name = f'{TARGET}{log_suffix}'

    print(f'Writing {log_to_write} as new log: {new_log_name}')
    print(f'Target wells: {len(selected_wells)} wells')

    try:
        write_back_to_petrel(results, log_to_write, clone_from=TARGET, new_log_name=new_log_name)
        print('✓ Write-back completed successfully!')
    except Exception as e:
        print(f'✗ Error during write-back: {str(e)}')

elif write_back_choice == 2:
    print('\n✓ Session ending without write-back.')
    print('Results are available in the "results" variable for further analysis.')

else:  # choice == 3
    print('\n✓ Continuing session with results in memory.')
    print('Results are available in the "results" variable.')
    print('You can manually call write_back_to_petrel() later if needed.')

# Connection management
if write_back_choice == 2:  # User chose to end session
    connection_choice = interactive_connection_management()

    if connection_choice == 1:
        print('\n✓ Ending Python session...')
        print('Thank you for using the Missing Log Imputation workflow!')
        # Note: In a real environment, you might want to call sys.exit() here
        # but we'll leave it as a message for safety
    else:
        print('\n✓ Session kept active for further analysis.')
        print('Petrel connection remains open.')


# ## 10 – Write-back Function (Enhanced)

def write_back_to_petrel(results_df, log_name_in_results, clone_from=None, new_log_name=None):
    """
    Clone an existing log and overwrite with imputed values.

    Args:
        results_df: DataFrame containing the results
        log_name_in_results: Column name in results_df to write back
        clone_from: Template log to clone from
        new_log_name: Name for the new log (auto-generated if None)
    """
    # Ensure we have the selected wells
    if 'selected_wells' not in locals():
        print('ERROR: Console selection not completed. Please run section 1 first.')
        return

    if clone_from is None:
        clone_from = selected_target_log  # Use the selected target log as template

    # Auto-generate new log name if not provided
    if new_log_name is None:
        if '_imputed' in log_name_in_results:
            new_log_name = log_name_in_results.replace('_imputed', '_ML_imputed')
        elif '_pred' in log_name_in_results:
            new_log_name = log_name_in_results.replace('_pred', '_ML_predicted')
        else:
            new_log_name = f'{log_name_in_results}_ML'

    print(f'Writing back {log_name_in_results} as {new_log_name} to {len(selected_wells)} wells...')
    success_count = 0
    
    for w in selected_wells:
        print(f'Updating {w.petrel_name}')
        well_df = results_df[results_df['WELL'] == w.petrel_name].set_index('MD')
        if well_df.empty:
            print(f'  ⚠ No data for well {w.petrel_name}, skipping')
            continue
            
        md = well_df.index.to_numpy()
        values = well_df[log_name_in_results].to_numpy()

        # Find a log to clone (or existing target)
        target_logs = [log for log in w.logs if log.petrel_name == new_log_name]
        if target_logs:
            log_obj = target_logs[0]
            print(f'  Using existing log {new_log_name}')
        else:
            # Clone template log
            template_logs = [log for log in w.logs if log.petrel_name == clone_from]
            if template_logs:
                template = template_logs[0]
                log_obj = template.clone(w, new_log_name)
                print(f'  Created new log {new_log_name} from template {clone_from}')
            else:
                print(f'  ✗ No template log {clone_from} found in well {w.petrel_name}, skipping')
                continue

        try:
            petrel_log_ref = petrel.well_logs[log_obj.path]
            petrel_log_ref.readonly = False
            petrel_log_ref.set_values(md, values)
            print(f'  ✓ Successfully updated {log_name_in_results} for {w.petrel_name}')
            success_count += 1
        except Exception as e:
            print(f'  ✗ Error updating {log_name_in_results} for {w.petrel_name}: {str(e)}')
    
    print(f'\nWrite-back completed: {success_count}/{len(selected_wells)} wells updated successfully.')




# ## 11 – Final Workflow Summary

print("\n" + "="*80)
print("MISSING LOG IMPUTATION WORKFLOW COMPLETED")
print("="*80)

try:
    print(f"✓ Project: {petrel.get_current_project_name()}")
    print(f"✓ Wells processed: {len(selected_wells)}")
    print(f"✓ Input logs used: {', '.join(selected_input_logs)}")
    print(f"✓ Target log processed: {selected_target_log}")
    print(f"✓ Prediction mode: {prediction_mode_name}")
    print(f"✓ Results shape: {results.shape}")

    # Calculate statistics based on prediction mode
    if prediction_mode == 1:  # Fill missing only
        missing_before = well_data[selected_target_log].isna().sum()
        total_samples = len(well_data)
        imputation_rate = missing_before / total_samples * 100
        print(f"✓ Missing values filled: {missing_before:,} ({imputation_rate:.1f}% of total data)")
    elif prediction_mode == 2:  # Cross-validation
        print(f"✓ Cross-validation completed with {test_percentage}% test data")
    else:  # Re-predict entire log
        total_samples = len(well_data)
        print(f"✓ Entire log re-predicted: {total_samples:,} samples")

    # Model performance summary
    if TARGET in model_results:
        best_model = model_results[TARGET]['best_model_name']
        best_mae = model_results[TARGET]['best_mae']
        print(f"✓ Best model: {best_model} (MAE: {best_mae:.3f})")

    print(f"\nWORKFLOW FEATURES USED:")
    print("✓ Console-based interactive selection")
    print("✓ Multiple ML models comparison")
    print("✓ Model performance visualization")
    print("✓ Interactive write-back options")
    print("✓ Flexible prediction modes")

    print(f"\nRESULTS AVAILABLE:")
    print("- results: Main DataFrame with all predictions")
    print("- model_results: Detailed model performance data")
    print("- All individual model predictions included")

except Exception as e:
    print(f"⚠ Workflow completed with some issues: {str(e)}")
    print("Please review the console output above for any errors.")

print("="*80)
print("Thank you for using the Enhanced Missing Log Imputation Workflow!")
print("="*80)


